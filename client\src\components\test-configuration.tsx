import { Clock, Play, RotateCcw } from "lucide-react";
import { Button } from "@/components/ui/button";
import { durationOptions } from "@shared/schema";
import { getTranslation } from "@/lib/i18n";

interface TestConfigurationProps {
  duration: number;
  setDuration: (duration: number) => void;
  isTestActive: boolean;
  onStart: () => void;
  onReset: () => void;
  language: string;
}

export function TestConfiguration({
  duration,
  setDuration,
  isTestActive,
  onStart,
  onReset,
  language
}: TestConfigurationProps) {
  return (
    <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-4 sm:p-6 mb-6 sm:mb-8">
      <div className="flex flex-col sm:flex-row sm:items-center gap-4">
        <div className="flex items-center space-x-2 flex-shrink-0">
          <Clock className="w-4 h-4 text-slate-500" />
          <span className="text-sm font-medium text-slate-700">{getTranslation(language, 'testDuration')}</span>
        </div>
        <div className="flex flex-wrap gap-2 flex-1">
          {durationOptions.map(option => (
            <Button
              key={option.value}
              variant={duration === option.value ? "default" : "outline"}
              size="sm"
              onClick={() => setDuration(option.value)}
              disabled={isTestActive}
              className="px-3 py-2 min-h-[44px] text-sm"
            >
              {option.label}
            </Button>
          ))}
        </div>
        <div className="flex flex-col xs:flex-row gap-2 flex-shrink-0">
          <Button
            onClick={onStart}
            disabled={isTestActive}
            className="px-4 py-2 min-h-[44px] text-sm whitespace-nowrap"
          >
            <Play className="w-4 h-4 mr-2" />
            {isTestActive ? getTranslation(language, 'testing') : getTranslation(language, 'startTest')}
          </Button>
          <Button
            variant="outline"
            onClick={onReset}
            className="px-4 py-2 min-h-[44px] text-sm whitespace-nowrap"
          >
            <RotateCcw className="w-4 h-4 mr-2" />
            {getTranslation(language, 'reset')}
          </Button>
        </div>
      </div>
    </div>
  );
}
