// API端点测试脚本
// 使用Node.js 18+的内置fetch API

const BASE_URL = 'https://typingtest-iawtcd8rg-wangyingyings-projects-8c5013f0.vercel.app';

async function testEndpoint(url, description) {
  console.log(`\n🔍 测试: ${description}`);
  console.log(`URL: ${url}`);
  
  try {
    const response = await fetch(url);
    const status = response.status;
    const statusText = response.statusText;
    
    console.log(`状态: ${status} ${statusText}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log(`✅ 成功`);
      console.log(`响应数据:`, JSON.stringify(data, null, 2));
      return { success: true, data, status };
    } else {
      const errorText = await response.text();
      console.log(`❌ 失败`);
      console.log(`错误信息:`, errorText);
      return { success: false, error: errorText, status };
    }
  } catch (error) {
    console.log(`❌ 网络错误`);
    console.log(`错误:`, error.message);
    return { success: false, error: error.message, status: 'NETWORK_ERROR' };
  }
}

async function runTests() {
  console.log('🚀 开始API端点测试...\n');
  console.log(`基础URL: ${BASE_URL}`);
  
  const tests = [
    {
      url: `${BASE_URL}/api/health`,
      description: '健康检查端点'
    },
    {
      url: `${BASE_URL}/api/text-samples`,
      description: '获取所有文本样本'
    },
    {
      url: `${BASE_URL}/api/text-samples/en/intermediate`,
      description: '获取英文中级文本样本'
    },
    {
      url: `${BASE_URL}/api/text-samples/zh/beginner`,
      description: '获取中文初级文本样本'
    },
    {
      url: `${BASE_URL}/api/text-samples/es/advanced`,
      description: '获取西班牙文高级文本样本'
    },
    {
      url: `${BASE_URL}/api/text-samples/invalid/invalid`,
      description: '测试无效语言/难度组合'
    }
  ];
  
  const results = [];
  
  for (const test of tests) {
    const result = await testEndpoint(test.url, test.description);
    results.push({
      ...test,
      ...result
    });
    
    // 添加延迟避免请求过快
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // 汇总结果
  console.log('\n' + '='.repeat(60));
  console.log('📊 测试结果汇总:');
  console.log('='.repeat(60));
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log(`✅ 成功: ${successful.length}/${results.length}`);
  console.log(`❌ 失败: ${failed.length}/${results.length}`);
  
  if (failed.length > 0) {
    console.log('\n❌ 失败的测试:');
    failed.forEach(test => {
      console.log(`  - ${test.description}: ${test.status} - ${test.error}`);
    });
  }
  
  if (successful.length > 0) {
    console.log('\n✅ 成功的测试:');
    successful.forEach(test => {
      console.log(`  - ${test.description}: ${test.status}`);
    });
  }
  
  // 特别检查文本样本
  const textSampleTest = successful.find(t => t.url.includes('/en/intermediate'));
  if (textSampleTest && textSampleTest.data) {
    console.log('\n📝 文本样本验证:');
    const sample = textSampleTest.data;
    if (sample.content && sample.title && sample.language && sample.difficulty) {
      console.log('✅ 文本样本结构正确');
      console.log(`  - 标题: ${sample.title}`);
      console.log(`  - 语言: ${sample.language}`);
      console.log(`  - 难度: ${sample.difficulty}`);
      console.log(`  - 内容长度: ${sample.content.length} 字符`);
    } else {
      console.log('❌ 文本样本结构不完整');
    }
  }
  
  console.log('\n🎯 建议:');
  if (failed.length === 0) {
    console.log('✅ 所有API端点都正常工作！');
    console.log('✅ 可以继续测试前端应用');
  } else {
    console.log('⚠️ 有API端点失败，需要检查:');
    console.log('  1. Vercel函数是否正确部署');
    console.log('  2. 路由配置是否正确');
    console.log('  3. 服务器日志中的错误信息');
  }
}

// 运行测试
runTests().catch(console.error);
