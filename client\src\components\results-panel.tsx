import { Trophy, Share2, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { getTranslation } from "@/lib/i18n";
import type { TypingTestState } from "@/hooks/use-typing-test";

interface ResultsPanelProps {
  testState: TypingTestState;
  language: string;
  difficulty: string;
  duration: number;
  onRetake: () => void;
}

export function ResultsPanel({
  testState,
  language,
  difficulty,
  duration,
  onRetake
}: ResultsPanelProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Clean up any debug elements that might exist
  if (typeof window !== 'undefined') {
    const debugDiv = document.getElementById('debug-info');
    if (debugDiv) {
      debugDiv.remove();
    }
  }



  const saveResultMutation = useMutation({
    mutationFn: async () => {
      return apiRequest('POST', '/api/test-results', {
        wpm: testState.wpm,
        accuracy: Math.round(testState.accuracy * 100), // Store as integer for precision
        duration,
        errors: testState.errors,
        language,
        difficulty,
        completedAt: new Date().toISOString(),
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/test-results'] });
      toast({
        title: getTranslation(language, 'resultsSaved'),
        description: getTranslation(language, 'resultsSavedDescription'),
      });
    },
    onError: () => {
      toast({
        title: getTranslation(language, 'failedToSaveResults'),
        description: getTranslation(language, 'failedToSaveDescription'),
        variant: "destructive",
      });
    },
  });

  const handleShare = async () => {
    const shareText = getTranslation(language, 'shareText')
      .replace('{wpm}', testState.wpm.toString())
      .replace('{accuracy}', testState.accuracy.toFixed(1));

    if (navigator.share) {
      try {
        await navigator.share({
          title: getTranslation(language, 'shareTitle'),
          text: shareText,
          url: window.location.href,
        });
      } catch (error) {
        // User cancelled sharing or share failed
      }
    } else {
      // Fallback to clipboard
      try {
        await navigator.clipboard.writeText(`${shareText} ${window.location.href}`);
        toast({
          title: getTranslation(language, 'copiedToClipboard'),
          description: getTranslation(language, 'copiedToClipboardDescription'),
        });
      } catch (error) {
        toast({
          title: getTranslation(language, 'shareFailed'),
          description: getTranslation(language, 'shareFailedDescription'),
          variant: "destructive",
        });
      }
    }
  };

  const averageWPM = 40; // This could be fetched from analytics
  const targetAccuracy = 95;

  return (
    <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-4 sm:p-6 lg:p-8 mb-6 sm:mb-8">
      <div className="text-center mb-6 sm:mb-8">
        <div className="inline-flex items-center justify-center w-12 h-12 sm:w-16 sm:h-16 bg-green-100 rounded-full mb-4">
          <Trophy className="w-6 h-6 sm:w-8 sm:h-8 text-green-600" />
        </div>
        <h2 className="text-xl sm:text-2xl font-bold text-slate-800 mb-2">{getTranslation(language, 'testComplete')}</h2>
        <p className="text-sm sm:text-base text-slate-600">{getTranslation(language, 'resultsDescription')}</p>
      </div>

      <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-6 mb-6 sm:mb-8">
        <div className="text-center p-3 sm:p-4 lg:p-6 bg-slate-50 rounded-lg">
          <div className="text-xl sm:text-2xl lg:text-3xl font-bold text-blue-600 mb-1 sm:mb-2">{testState.wpm}</div>
          <div className="text-xs sm:text-sm font-medium text-slate-600">{getTranslation(language, 'wordsPerMinute')}</div>
          <div className="text-xs text-slate-500 mt-1 hidden sm:block">{getTranslation(language, 'average')}: {averageWPM} {getTranslation(language, 'wpm')}</div>
        </div>

        <div className="text-center p-3 sm:p-4 lg:p-6 bg-slate-50 rounded-lg">
          <div className="text-xl sm:text-2xl lg:text-3xl font-bold text-green-600 mb-1 sm:mb-2">{testState.accuracy.toFixed(1)}%</div>
          <div className="text-xs sm:text-sm font-medium text-slate-600">{getTranslation(language, 'accuracy')}</div>
          <div className="text-xs text-slate-500 mt-1 hidden sm:block">{getTranslation(language, 'target')}: {targetAccuracy}%+</div>
        </div>

        <div className="text-center p-3 sm:p-4 lg:p-6 bg-slate-50 rounded-lg">
          <div className="text-xl sm:text-2xl lg:text-3xl font-bold text-amber-600 mb-1 sm:mb-2">{duration}s</div>
          <div className="text-xs sm:text-sm font-medium text-slate-600">{getTranslation(language, 'timeTaken')}</div>
          <div className="text-xs text-slate-500 mt-1 hidden sm:block">{getTranslation(language, 'testDurationLabel')}</div>
        </div>

        <div className="text-center p-3 sm:p-4 lg:p-6 bg-slate-50 rounded-lg">
          <div className="text-xl sm:text-2xl lg:text-3xl font-bold text-red-600 mb-1 sm:mb-2">{testState.errors}</div>
          <div className="text-xs sm:text-sm font-medium text-slate-600">{getTranslation(language, 'totalErrors')}</div>
          <div className="text-xs text-slate-500 mt-1 hidden sm:block">{getTranslation(language, 'incorrectKeystrokes')}</div>
        </div>
      </div>

      <div className="border-t border-slate-200 pt-4 sm:pt-6">
        <div className="flex flex-col gap-3 sm:gap-4">
          <Button onClick={onRetake} className="w-full sm:w-auto px-6 py-3 min-h-[44px]">
            <RotateCcw className="w-4 h-4 mr-2" />
            {getTranslation(language, 'takeAnotherTest')}
          </Button>
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
            <Button variant="outline" onClick={handleShare} className="flex-1 sm:flex-none px-6 py-3 min-h-[44px]">
              <Share2 className="w-4 h-4 mr-2" />
              {getTranslation(language, 'shareResults')}
            </Button>
            <Button
              variant="outline"
              onClick={() => saveResultMutation.mutate()}
              disabled={saveResultMutation.isPending}
              className="flex-1 sm:flex-none px-6 py-3 min-h-[44px]"
            >
              {saveResultMutation.isPending ? getTranslation(language, 'saving') : getTranslation(language, 'saveResults')}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
