<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Analytics Test Page</title>
    
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RCM4MX2590"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-RCM4MX2590');
      
      // 添加调试信息
      console.log('Google Analytics 脚本已加载');
      console.log('dataLayer:', window.dataLayer);
    </script>
    
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f5f5f5;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        .result {
            background: #e7f3ff;
            padding: 10px;
            margin: 10px 0;
            border-left: 4px solid #007cba;
        }
    </style>
</head>
<body>
    <h1>Google Analytics 测试页面</h1>
    
    <div class="test-section">
        <h2>1. 基本检查</h2>
        <button onclick="checkBasics()">检查基本配置</button>
        <div id="basics-result" class="result" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 网络请求检查</h2>
        <p>打开浏览器开发者工具的 Network 标签，然后刷新页面。查找以下请求：</p>
        <ul>
            <li><code>https://www.googletagmanager.com/gtag/js?id=G-RCM4MX2590</code></li>
            <li><code>https://www.google-analytics.com/g/collect</code> (数据发送)</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>3. 事件测试</h2>
        <button onclick="sendTestEvent()">发送测试事件</button>
        <div id="event-result" class="result" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <h2>4. 控制台检查</h2>
        <p>在浏览器控制台中运行以下命令：</p>
        <pre><code>
// 检查 gtag 函数
typeof window.gtag

// 检查 dataLayer
window.dataLayer

// 发送测试事件
gtag('event', 'test_event', {
  'custom_parameter': 'test_value'
});
        </code></pre>
    </div>
    
    <div class="test-section">
        <h2>5. 可能的问题</h2>
        <ul>
            <li><strong>广告拦截器：</strong> 禁用浏览器扩展或在隐身模式下测试</li>
            <li><strong>网络问题：</strong> 检查是否能访问 googletagmanager.com</li>
            <li><strong>CSP 策略：</strong> 检查是否有内容安全策略阻止脚本</li>
            <li><strong>开发环境：</strong> 某些开发工具可能会阻止跟踪脚本</li>
        </ul>
    </div>

    <script>
        function checkBasics() {
            const resultDiv = document.getElementById('basics-result');
            resultDiv.style.display = 'block';
            
            let html = '<h3>基本检查结果：</h3>';
            
            // 检查 gtag 函数
            if (typeof window.gtag === 'function') {
                html += '<p>✅ gtag 函数已定义</p>';
            } else {
                html += '<p>❌ gtag 函数未定义</p>';
            }
            
            // 检查 dataLayer
            if (window.dataLayer && Array.isArray(window.dataLayer)) {
                html += `<p>✅ dataLayer 已定义，包含 ${window.dataLayer.length} 个项目</p>`;
                html += `<p>dataLayer 内容: <code>${JSON.stringify(window.dataLayer, null, 2)}</code></p>`;
            } else {
                html += '<p>❌ dataLayer 未定义或不是数组</p>';
            }
            
            // 检查脚本标签
            const gtagScript = document.querySelector('script[src*="googletagmanager.com"]');
            if (gtagScript) {
                html += `<p>✅ 找到 gtag 脚本标签: ${gtagScript.src}</p>`;
            } else {
                html += '<p>❌ 未找到 gtag 脚本标签</p>';
            }
            
            resultDiv.innerHTML = html;
        }
        
        function sendTestEvent() {
            const resultDiv = document.getElementById('event-result');
            resultDiv.style.display = 'block';
            
            if (typeof window.gtag === 'function') {
                try {
                    window.gtag('event', 'test_button_click', {
                        'event_category': 'engagement',
                        'event_label': 'test_page',
                        'custom_parameter': 'test_value_' + Date.now()
                    });
                    
                    resultDiv.innerHTML = `
                        <h3>测试事件已发送！</h3>
                        <p>✅ 事件名称: test_button_click</p>
                        <p>✅ 时间戳: ${new Date().toLocaleString()}</p>
                        <p>请检查 Network 标签中是否有新的 collect 请求</p>
                    `;
                } catch (error) {
                    resultDiv.innerHTML = `
                        <h3>发送事件时出错：</h3>
                        <p>❌ 错误信息: ${error.message}</p>
                    `;
                }
            } else {
                resultDiv.innerHTML = `
                    <h3>无法发送事件</h3>
                    <p>❌ gtag 函数不可用</p>
                `;
            }
        }
        
        // 页面加载完成后自动检查
        window.addEventListener('load', function() {
            console.log('页面加载完成，开始检查 Google Analytics...');
            
            setTimeout(() => {
                console.log('延迟检查结果:');
                console.log('gtag 函数类型:', typeof window.gtag);
                console.log('dataLayer:', window.dataLayer);
                
                // 自动运行基本检查
                checkBasics();
            }, 2000);
        });
        
        // 监听 gtag 脚本加载
        const gtagScript = document.querySelector('script[src*="googletagmanager.com"]');
        if (gtagScript) {
            gtagScript.onload = function() {
                console.log('✅ gtag 脚本加载完成');
            };
            gtagScript.onerror = function() {
                console.log('❌ gtag 脚本加载失败');
            };
        }
    </script>
</body>
</html>
