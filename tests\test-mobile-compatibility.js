// Mobile Compatibility Test Script
// Run this in the browser console to test mobile optimizations

function testMobileCompatibility() {
  console.log('🧪 Testing Mobile Compatibility...\n');

  const results = {
    viewport: false,
    touchTargets: false,
    fontSizes: false,
    responsiveLayout: false,
    inputHandling: false,
    performance: false
  };

  // Test 1: Viewport Configuration
  console.log('1. Testing Viewport Configuration...');
  const viewport = document.querySelector('meta[name="viewport"]');
  if (viewport && viewport.content.includes('width=device-width')) {
    results.viewport = true;
    console.log('✅ Viewport is properly configured');
  } else {
    console.log('❌ Viewport configuration missing or incorrect');
  }

  // Test 2: Touch Target Sizes
  console.log('\n2. Testing Touch Target Sizes...');
  const buttons = document.querySelectorAll('button, [role="button"]');
  let touchTargetsOk = true;
  
  buttons.forEach(button => {
    const rect = button.getBoundingClientRect();
    if (rect.width < 44 || rect.height < 44) {
      touchTargetsOk = false;
      console.log(`❌ Small touch target found: ${rect.width}x${rect.height}px`);
    }
  });
  
  if (touchTargetsOk) {
    results.touchTargets = true;
    console.log('✅ All touch targets meet minimum size requirements');
  }

  // Test 3: Font Sizes
  console.log('\n3. Testing Font Sizes...');
  const inputs = document.querySelectorAll('input, select, textarea');
  let fontSizesOk = true;
  
  inputs.forEach(input => {
    const styles = window.getComputedStyle(input);
    const fontSize = parseInt(styles.fontSize);
    if (fontSize < 16) {
      fontSizesOk = false;
      console.log(`❌ Small font size found: ${fontSize}px (should be ≥16px to prevent zoom)`);
    }
  });
  
  if (fontSizesOk) {
    results.fontSizes = true;
    console.log('✅ All input font sizes are appropriate for mobile');
  }

  // Test 4: Responsive Layout
  console.log('\n4. Testing Responsive Layout...');
  const hasResponsiveClasses = document.body.innerHTML.includes('sm:') || 
                               document.body.innerHTML.includes('md:') || 
                               document.body.innerHTML.includes('lg:');
  
  if (hasResponsiveClasses) {
    results.responsiveLayout = true;
    console.log('✅ Responsive classes detected');
  } else {
    console.log('❌ No responsive classes found');
  }

  // Test 5: Input Handling
  console.log('\n5. Testing Input Handling...');
  const hiddenInputs = document.querySelectorAll('input[type="text"]');
  const mobileInputHandler = document.querySelector('[class*="mobile"]');
  
  if (hiddenInputs.length > 0 || mobileInputHandler) {
    results.inputHandling = true;
    console.log('✅ Mobile input handling detected');
  } else {
    console.log('❌ No mobile input handling found');
  }

  // Test 6: Performance
  console.log('\n6. Testing Performance...');
  const performanceEntries = performance.getEntriesByType('navigation');
  if (performanceEntries.length > 0) {
    const loadTime = performanceEntries[0].loadEventEnd - performanceEntries[0].loadEventStart;
    if (loadTime < 3000) {
      results.performance = true;
      console.log(`✅ Page load time: ${loadTime}ms (good)`);
    } else {
      console.log(`⚠️ Page load time: ${loadTime}ms (could be improved)`);
    }
  }

  // Summary
  console.log('\n📊 Test Results Summary:');
  console.log('========================');
  Object.entries(results).forEach(([test, passed]) => {
    console.log(`${passed ? '✅' : '❌'} ${test}: ${passed ? 'PASS' : 'FAIL'}`);
  });

  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  const score = Math.round((passedTests / totalTests) * 100);
  
  console.log(`\n🎯 Overall Score: ${score}% (${passedTests}/${totalTests} tests passed)`);
  
  if (score >= 80) {
    console.log('🎉 Excellent mobile compatibility!');
  } else if (score >= 60) {
    console.log('👍 Good mobile compatibility, some improvements possible');
  } else {
    console.log('⚠️ Mobile compatibility needs improvement');
  }

  return results;
}

// Test device detection
function testDeviceDetection() {
  console.log('\n📱 Testing Device Detection...');
  
  const isMobile = window.innerWidth < 768;
  const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  
  console.log(`Screen width: ${window.innerWidth}px`);
  console.log(`Is mobile: ${isMobile}`);
  console.log(`Is touch device: ${isTouchDevice}`);
  console.log(`User agent: ${navigator.userAgent}`);
}

// Test accessibility
function testAccessibility() {
  console.log('\n♿ Testing Accessibility...');
  
  const results = {
    altTexts: true,
    focusVisible: true,
    semanticHTML: true,
    ariaLabels: true
  };

  // Check for images without alt text
  const images = document.querySelectorAll('img');
  images.forEach(img => {
    if (!img.alt) {
      results.altTexts = false;
      console.log('❌ Image without alt text found');
    }
  });

  // Check for focus indicators
  const focusableElements = document.querySelectorAll('button, input, select, textarea, a[href]');
  // This would need actual focus testing in a real scenario

  console.log('Accessibility check completed');
  return results;
}

// Export functions for manual testing
window.testMobileCompatibility = testMobileCompatibility;
window.testDeviceDetection = testDeviceDetection;
window.testAccessibility = testAccessibility;

console.log('🔧 Mobile compatibility test functions loaded!');
console.log('Run testMobileCompatibility() to start testing');
console.log('Run testDeviceDetection() to test device detection');
console.log('Run testAccessibility() to test accessibility features');
