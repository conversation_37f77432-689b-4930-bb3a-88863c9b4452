// 简单的配置验证脚本
import fs from 'fs';
import path from 'path';

console.log('🔍 验证Vercel部署配置...\n');

// 检查必要文件
const requiredFiles = [
  'vercel.json',
  'api/index.ts',
  'dist/public/index.html',
  'package.json'
];

let allFilesExist = true;

requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file} - 存在`);
  } else {
    console.log(`❌ ${file} - 缺失`);
    allFilesExist = false;
  }
});

// 检查package.json中的脚本
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  
  console.log('\n📦 检查package.json脚本:');
  
  if (packageJson.scripts['vercel-build']) {
    console.log('✅ vercel-build 脚本存在');
  } else {
    console.log('❌ vercel-build 脚本缺失');
    allFilesExist = false;
  }
  
  if (packageJson.scripts['build:vercel']) {
    console.log('✅ build:vercel 脚本存在');
  } else {
    console.log('❌ build:vercel 脚本缺失');
    allFilesExist = false;
  }
} catch (error) {
  console.log('❌ 无法读取package.json');
  allFilesExist = false;
}

// 检查vercel.json配置
try {
  const vercelConfig = JSON.parse(fs.readFileSync('vercel.json', 'utf8'));
  
  console.log('\n⚙️ 检查vercel.json配置:');
  
  if (vercelConfig.builds && vercelConfig.builds.length > 0) {
    console.log('✅ builds 配置存在');
  } else {
    console.log('❌ builds 配置缺失');
  }
  
  if (vercelConfig.routes && vercelConfig.routes.length > 0) {
    console.log('✅ routes 配置存在');
  } else {
    console.log('❌ routes 配置缺失');
  }
  
  if (vercelConfig.buildCommand) {
    console.log('✅ buildCommand 配置存在');
  } else {
    console.log('❌ buildCommand 配置缺失');
  }
} catch (error) {
  console.log('❌ 无法读取vercel.json');
  allFilesExist = false;
}

// 检查构建输出
console.log('\n🏗️ 检查构建输出:');
const distPath = 'dist/public';
if (fs.existsSync(distPath)) {
  const files = fs.readdirSync(distPath);
  console.log(`✅ 构建输出目录存在，包含 ${files.length} 个文件`);
  
  if (files.includes('index.html')) {
    console.log('✅ index.html 存在');
  } else {
    console.log('❌ index.html 缺失');
  }
  
  const assetsDir = path.join(distPath, 'assets');
  if (fs.existsSync(assetsDir)) {
    const assetFiles = fs.readdirSync(assetsDir);
    console.log(`✅ assets 目录存在，包含 ${assetFiles.length} 个文件`);
  } else {
    console.log('❌ assets 目录缺失');
  }
} else {
  console.log('❌ 构建输出目录不存在，请运行 npm run vercel-build');
  allFilesExist = false;
}

console.log('\n' + '='.repeat(50));

if (allFilesExist) {
  console.log('🎉 所有配置检查通过！项目已准备好部署到Vercel。');
  console.log('\n📋 下一步:');
  console.log('1. git add .');
  console.log('2. git commit -m "Add Vercel deployment configuration"');
  console.log('3. git push');
  console.log('4. 在Vercel Dashboard中重新部署项目');
} else {
  console.log('⚠️ 配置检查失败，请修复上述问题后重试。');
}
